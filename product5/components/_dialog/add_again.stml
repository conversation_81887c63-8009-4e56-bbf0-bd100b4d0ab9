<style>
</style>
<template>
	<view :s="monitor" class="page_box xy_center" :style="`display:${dataMore.show?'flex':'none'};background: #FFF;`">
		<view class="watermark_box">
			{
				G.watermark && 
				<image class="xy_100" :src="G.watermark" mode="aspectFill" thumbnail="false"></image>
			}
		</view>
		{
			dataMore.show && [
				<view style="margin:-40px 0 40px;">
					<view class="xy_center" :style="`width:116px;height:116px;border-radius:50%;background:${colorRgba(G.appTheme,0.1)};`">
						<a-iconfont name="fenxiang1" :color="G.appTheme" :size="G.appFontSize+80"/>
					</view>
				</view>,
				<view style="margin-bottom:15px;">
					<text :style="`${loadConfiguration(6)}color: #333;font-weight: 600;`">{dataMore.title||'提交成功'}</text>
				</view>,
				<view style="margin-bottom:30px;">
					<view v-for="(item,index) in dataMore.text" class="xy_center">
						<text :style="`${loadConfiguration()}color: #666;`">{item}{index==dataMore.text.length-1?(time+'s'):''}</text>
					</view>
				</view>,
				<view>
					{
						dataMore.again && 
						<z-button @click="again()" round style="min-width:132px;padding:7px 15px;margin-bottom:20px;" :color="G.appTheme" :text="dataMore.againText||'再次提交'"></z-button>
					}
				</view>,
				<z-button @click="close()" round plain style="min-width:132px;padding:7px 15px;" :color="G.appTheme" :text="dataMore.backText||'返回'"></z-button>
			]
		}
	</view>
</template>
<script>
import "../../components/_zy/z-button.stml";
import { colorRgba, G,loadConfiguration } from '../../script/_zy/general';
import { shareAddress } from '../../script/_zy/myjs';
import { hideProgress, platform, showProgress } from '../../script/_zy/t';
export default {
	name: 'add-again',
	props:{
		dataMore:Object,
	},
	data() {
		return {
			show:false,
			time:3,
		}
	},
	computed:{
		monitor(){
			if(this.props.dataMore.show != this.show){
				this.show = this.props.dataMore.show;
				if(this.show){
					this.time = this.props.dataMore.exitTime || 3;
					this.startTime();
				}
			}
		},
	},
	methods:{
		close(){
			this.timeTask && clearTimeout(this.timeTask);
			this.fire('close', this.props.dataMore);
		},
		again(){
			this.timeTask && clearTimeout(this.timeTask);
			this.fire('again', this.props.dataMore);
			this.props.dataMore.show = false;
		},
		startTime(){
			if(this.time <= 0){
				this.close();
			}else{
				if(!this.show){//提前关闭 不再执行
					return;
				}
				this.timeTask = setTimeout(() => {
					this.time--;
					this.startTime();
				}, 1000);
			}
		},
	}
};
</script>
