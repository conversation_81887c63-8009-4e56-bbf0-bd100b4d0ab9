<style>
</style>
<template>
	<view :style="`display:${dataMore.show?'flex':'none'};position:absolute;z-index:999;left:0;right:0;bottom:0;`">
		{
			dataMore.show &&
			<view :style="`padding:10px 16px ${safeArea().bottom+70}px 16px;`">
				<view class="flex_row" style="background: #FFFFFF;box-shadow: 0px 2px 10px 1px rgba(0,0,0,0.08);border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px;padding:14px 10px;">
					<text :style="`${loadConfiguration()}color:#333;font-weight: 600;padding: 0px 10px;flex:1;`">已成功添加收藏</text>
					<view @click="openCollect" class="flex_row">
						<text :style="`${loadConfiguration()}color:${G.appTheme};margin-right:6px;`">前往查看</text>
						<a-iconfont style="transform: rotate(180deg) scale(0.7,0.7);" name="fanhui1" :color="G.appTheme" :size="G.appFontSize-4"/>
					</view>
				</view>
			</view>
		}
	</view>
</template>
<script>
import { G,loadConfiguration } from '../../script/_zy/general';
import { openWin_collect } from '../../script/_zy/openDetails';
import { safeArea } from '../../script/_zy/t';
export default {
	name: 'collect-ok',
	props:{
		dataMore:Object,
	},
	methods:{
		openCollect(){
			openWin_collect({});
		},
	}
};
</script>
