<template>
	<view class="page_box xy_center" @click="closeStop" :style="`background:rgba(0,0,0,0.4);`">
		<view style="padding-top:43px;background:transparent;">
			<view style="position:absolute;z-index:999; top:0; left:0; right:0; align-items: center;">
				<image style="width: 160px; height: 93px;" mode="aspectFit" thumbnail="false" :src="`${shareAddress(1)}image/icon_update_version_${G.sysSign}.png`" />
			</view>
			<view style="background: #FFF;border-radius: 5px;padding: 15px 20px;width:320px;">
				<view class="watermark_box">
					{
						G.watermark && 
						<image class="xy_100" :src="G.watermark" mode="aspectFill" thumbnail="false"></image>
					}
				</view>
				<text :style="loadConfiguration(4)+'font-weight: 800;text-align: center;margin:37px 0 15px 0;'">更新版本</text>
				<text :style="`${loadConfiguration(1)}color: #333333;`">{content}</text>
				<view :style="`margin-top:20px;padding:0 35px;`">
					<view>
						{
							downloadText && 
							<view style="align-items: center;">
								<view :style="`align-items:center;justify-content:center;height:40px;width:200px;border-radius:999px;overflow: hidden;border:1px solid ${G.appTheme};`">
									<text :style="loadConfiguration()+`color:${G.appTheme};`">{downloadText}</text>
									<view :style="`position: absolute;left:0;right:0;width:${download}%;height:100%;border-radius:999px;background:${colorRgba(G.appTheme,0.3)}`">

									</view>
								</view>
							</view>
						}
					</view>
					<view>
						{
							!downloadText && 
							<view style="align-items: center;">
								<z-button style="height:40px;width:200px;" @click="agree()" round :text="btnName"></z-button>
							</view>
						}
					</view>
				</view>
				<text :style="loadConfiguration(-2)+'margin-top:12px;text-align: center;color: #999999;'">V {dataMore.num}</text>
			</view>
		</view>
		<view>
			{
				showClose && 
				<view @click="close()" style="margin-top:15px;">
					<a-iconfont style="transform: rotate(45deg);" name="gengduo1" color="#FFF" :size="G.appFontSize+16"/>
				</view>
			}
		</view>
	</view>
</template>
<script>
import "../../components/_zy/z-button.stml";
import {shareAddress,appUrl} from "../../script/_zy/myjs.js";
import {G,loadConfiguration,stopBubble,colorRgba} from "../../script/_zy/general.js";
import {isParameters,openWin,showProgress,hideProgress,ajax,setPrefs} from "../../script/_zy/t.js";
export default {
	name: 'version-update',
	props:{
		dataMore: Object
	},
	installed() {
		if(this.content.indexOf("|") == 0 || this.content.indexOf("?") == 0){
			this.content = this.content.substring(1);
			this.showClose = true;
		}
		if(this.props.dataMore.type == 'checkSmartUpdate'){
			setTimeout(() => {
				this.agree();
			}, 200);
		}
	},
	data() {
		return {
			showClose:false,
			content: this.props.dataMore.content || "",
			downloadText:"",
			download:0,
			btnName:`立即${this.props.dataMore.type == 'checkUpdate'?'更新':'修复'}`,
		};
	},
	methods: {
		closeStop(e){
			stopBubble(e);
		},
		agree(){
			if(this.btnName == "修复失败,点击关闭"){
				this.close();
				return;
			}
			if(this.props.dataMore.type == 'checkUpdate'){
				if (api.systemType == "ios") {
					showProgress();
					ajax({u:appUrl() + 'zyAppDownloadUrl/findDownLoadUrl?'}, 'findDownLoadUrl', (ret, err)=> {
						hideProgress();
						if (ret && ret.code == "200" && ret.data) {
							api.installApp({ appUri: ret.data });
						}else{
							this.downloadText = "";
							this.btnName = ret?(ret.message || ret.data):"下载失败,点击重试";
						}
					}, "获取ios下载",'get',{},{"Authorization":""});
					return;
				}
				this.showClose = false;
				this.download = 0;
				this.downloadText = "下载中...";
				api.download({
					url: this.props.dataMore.url,
					encode: false,
					report: true,
					cache: false,
					allowResume: false,
				}, (ret, err)=> {
					console.log("更新版本："+JSON.stringify(ret?ret:err));
					if (ret) {
						switch (ret.state) {
							case 0:
								var nowProgress = ret.percent;
								this.download = parseInt(nowProgress);
								this.downloadText = parseInt(nowProgress) + "%";
								break;
							case 1:
								this.downloadText = "";
								this.btnName = "重新下载";
								api.installApp({ appUri: ret.savePath });
								break;
							case 2:
								this.downloadText = "";
								this.btnName = "下载失败,点击重试";
								break;
						}
					} else {
						this.downloadText = "";
						this.btnName = "下载失败,点击重试";
					}
				});
				return;
			}
			this.showClose = false;
			this.download = 0;
			this.downloadText = "修复中...";
			api.require('mam').startSmartUpdate((ret, err)=> {
				console.log("更新修复包："+JSON.stringify(ret?ret:err));
				if (ret) {
					var state = ret.state;
					var total = ret.total;
					var current = ret.current;
					var progress = ret.progress;
					switch (Number(state)) {
						case 3:
							this.downloadText = "";
							this.btnName = "修复完成,正在重启...";
							setTimeout(()=> {
								api.rebootApp();
							}, 300);
							break;
						case 4:
							this.downloadText = "";
							this.btnName = "修复失败,点击关闭";
							setPrefs('smartupdatefinish', 'false');
							break;
						default:
							this.download = parseInt(progress);
							this.downloadText = parseInt(progress) + "%";
							this.downloadText = current + "-" + total + "（" + this.download + "%）";
							break;
					}
				} else {
					this.downloadText = "";
					this.btnName = "修复失败,点击关闭";
					setPrefs('smartupdatefinish', 'false');
				}
			});
		},
		close(){
			this.props.dataMore.show = false;
		},
	}
};
</script>
