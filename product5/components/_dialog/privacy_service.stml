<template>
	<view class="page_box xy_center" @click="closeStop" style="background:rgba(0,0,0,0.4);">
		<view style="background: #FFF;border-radius: 5px;padding: 15px 20px;width:275px;">
			<view class="watermark_box">
				{
					G.watermark && 
					<image class="xy_100" :src="G.watermark" mode="aspectFill" thumbnail="false"></image>
				}
			</view>
			<text :style="loadConfiguration(10)+'font-weight: 800;text-align: center;margin:10px 0 15px 0;text-decoration:underline;'">隐私政策</text>
			<view style="flex-direction:row;flex-wrap: wrap;">
				<text v-for="(item,index) in privacyText" @click="chageAgreement(index)" :style="loadConfiguration(1)+`line-height:${G.appFontSize*1.5}px;${this.dealwithStyle(index)}`">{item}</text>
			</view>
			<view class="xy_center" :style="`margin-top:25px;`">
				<z-button style="width:150px;" :size="1" @click="agree()" round :color="G.appTheme">同意</z-button>
			</view>
			<view>
				<text @click="refuse()" :style="loadConfiguration(-1)+'margin-top:10px;text-align: center;padding:8px 0;'">不同意并退出</text>
			</view>
		</view>
	</view>
</template>
<script>
import "../../components/_zy/z-button.stml";
import {G,loadConfiguration,stopBubble} from "../../script/_zy/general.js";
import { openWin_apptext } from '../../script/_zy/openDetails';
import {isParameters,openWin,setPrefs} from "../../script/_zy/t.js";
export default {
	name: 'privacy-service',
	props:{
		dataMore: Object
	},
	data() {
		return {
			privacyText:"本应用尊重并保护所有用户的个人隐私权。为了给您提供更准确、更有个性化的服务，本应用会按照隐私政策的规定使用和披露您的个人信息。可阅读服务协议和隐私政策。",
		};
	},
	methods: {
		closeStop(e){
			stopBubble(e);
		},
		dealwithStyle(index){
			let color = index>65&&index<70||index>70&&index<75?('color:'+G.appTheme+';text-decoration: underline;'):'color:#333;';
			let marginLeft = index == 66 || index == 71?'margin-left:9px;':'';
			let marginRight = index == 69 || index == 74?'margin-right:9px;':'';
			return `${color}${marginLeft}${marginRight}`;
		},
		chageAgreement(_index){
			if(isParameters(_index)){
				if(_index > 65 && _index < 70){
					openWin_apptext({title:"服务协议",pt:"fwxy",id:"fwxy"});
					return;
				}else if(_index > 70 && _index < 75){
					openWin_apptext({title:"隐私政策",pt:"yszc",id:"yszc"});
					return;
				}
			}
		},
		agree(){
			setPrefs("isFirstOpen", "true");
			this.fire('agree', {});
		},
		refuse(){
			this.fire('refuse', {});
		},
	}
};
</script>
