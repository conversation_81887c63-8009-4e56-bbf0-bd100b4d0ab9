<style>
	.toast_ai_box{ position:absolute;z-index:1000;left:0;right:0;top:0;bottom:0; background:rgba(0,0,0,0.4); }
	.toast_ai_warp{ width: 320px; min-height: 277px; padding: 85px 19px 20px; }
	.toast_ai_img{ position: absolute; width: 320px; height: 277px; }
	.toast_ai_bg{ position: absolute; left:0; right:0;top:50px;bottom:0; background: #FFF; border-radius: 20px; }
	.toast_ai_item{ margin-top:10px;flex-direction:row;align-items: flex-start; }
	.toast_ai_item_icon{
		margin-top:4px;
		margin-right: 7px;
		border-radius: 30px;
		
	}
</style>
<template>
	<view class="toast_ai_box" :style="`display:${dataMore.show?'flex':'none'};`">
		{
			dataMore.show && 
			<view class="xy_100 xy_center">
				<view>
					<view class="toast_ai_bg"></view>
					<image class="toast_ai_img" mode="aspectFill" thumbnail="false" :src="dataMore.bgUrl" />
					<view class="toast_ai_warp">
						<view style="min-height:126px;">
							{
								isArray(dataMore.content) && 
								dataMore.content.map((item, index) => {return [
									<view class="toast_ai_item">
										<view class="toast_ai_item_icon" :style="`background:${G.appTheme}`">
											<a-iconfont name="xiangxia1" style="transform: rotate(-90deg);" color="#FFF" :size="G.appFontSize"/>
										</view>
										<text :style="`${loadConfiguration()}line-height:${G.appFontSize*1.5}px;flex:1;color:#333;font-weight: 400;`">{item}</text>
									</view>
								]})
							}
						</view>
						<view style="margin-top:20px" class="xy_center">
							<z-button @click="pageClick()" round style="min-width:210px;" :color="G.appTheme">{dataMore.btnText}</z-button>
						</view>
					</view>
				</view>
				<view @click="closePage()" style="margin-top:20px;">
					<a-iconfont style="transform: rotate(45deg);" name="gengduo1" color="#FFF" :size="G.appFontSize+26"/>
				</view>
			</view>
		}
	</view>
</template>
<script>
import "../../components/_zy/z-button.stml";
import { G,loadConfiguration } from '../../script/_zy/general';
import { isArray, safeArea, setPrefs } from '../../script/_zy/t';
export default {
	name: 'toast-ai',
	props:{
		dataMore:Object,

	},
	methods:{
		closePage(_type){
			this.props.dataMore.show = false;
			setPrefs(this.props.dataMore.key + G.uId,"true");
		},
		pageClick(){
			this.fire('click', {});
			this.closePage();
		}
	}
};
</script>
