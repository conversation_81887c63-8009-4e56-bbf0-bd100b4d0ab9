<style>
</style>
<template>
	<view :s="monitor" class="page_box" :style="`display:${dataMore.show?'flex':'none'};background:rgba(0,0,0,0);`">
		{
			(dataMore.show && (platform()=='web' || G.watermark)) && 
			<frame :id="frameName" class="xy_100" :name="frameName" :url="`${shareAddress()}html/previewerImg.html`" :pageParam="{name:frameName}" useWKWebView scaleEnabled allowEdit></frame>
		}
	</view>
</template>
<script>
import { G } from '../../script/_zy/general';
import { shareAddress } from '../../script/_zy/myjs';
import { addEventListener, hideProgress, platform, sendEvent, showProgress, toast } from '../../script/_zy/t';
export default {
	name: 'previewer-img',
	props:{
		dataMore:Object,
	},
	data() {
		return {
			show:false,
			frameName:"previewerImg"
		}
	},
	computed:{
		monitor(){
			if(this.props.dataMore.show != this.show){
				this.show = this.props.dataMore.show;
				if(this.show){
					this.props.dataMore.watermark = G.watermark;
					this.baseInit();
				}else{
					if(platform() == "app" && this.UIPhotoViewer){
						this.UIPhotoViewer.close();
					}
				}
			}
			if(this.show){
				if(this.nowParam != JSON.stringify(this.props.dataMore)){
					this.nowParam = JSON.stringify(this.props.dataMore);
					this.sendMessage();
				}
			}
		},
	},
	methods:{
		baseInit(){
			var data = this.props.dataMore;
			switch(platform()){
				case "app":
					if(G.watermark){//有水印使用web
						addEventListener(this.frameName+'_msg',()=>{
							this.closePage();
						});
						api.setFrameClient({ frameName: this.frameName }, (ret, err)=> {
							if(ret.state == 2){
								this.isLoading = true;
								setTimeout(() => {
									this.sendMessage();
								}, 400);
							}
						});
						return;
					}
					this.UIPhotoViewer = api.require('UIPhotoViewer');
					this.UIPhotoViewer.open({
						images: data.imgs,
						activeIndex:data.index,
						gestureClose:true,
						bgColor: '#000'
					}, (ret, err)=> {
						switch(ret.eventType){
							case "click":
								this.closePage();
								break;
							case "gestureColse":
								this.UIPhotoViewer = null;
								this.closePage();
								break;
							case "longPress":
								var nowImg = data.imgs[ret.index];
								api.actionSheet({
									buttons: ['保存到相册'],
									cancelTitle: '取消',
								},(ret)=>{
									switch(ret.buttonIndex){
										case 1:
											api.saveMediaToAlbum({
												path: nowImg
											}, (ret)=> {
												toast(ret && ret.status?'已保存到手机相册':'保存失败');
											});
											break;
									}
								})
								break;
						}
					});
					break;
				case "mp":
					wx.previewImage({
						urls: data.imgs,
						current: data.imgs[data.index],
						complete:()=>{
							this.closePage();
						}
					})
					break;
				case "web":
					window.addEventListener('message', (event)=> {
						this.closePage();
					});
					document.getElementById(this.frameName).addEventListener('load', ()=> {
						this.isLoading = true;
						this.sendMessage();
					});
					break;
			}
		},
		closePage(){
			this.props.dataMore.show = false;
		},
		sendMessage(){
			if(this.isLoading && this.props.dataMore.show){
				if(platform() == 'web'){
					if(!document.getElementById(this.frameName)){
						return;
					}
					var targetWindow = document.getElementById(this.frameName).contentWindow;
					targetWindow.postMessage(this.props.dataMore, '*');
				}else if(platform() == 'app'){
					sendEvent(this.frameName+'_open',this.props.dataMore);
				}else{
					
				}
			}
		},
	}
};
</script>
