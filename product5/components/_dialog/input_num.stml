<style>
	.numInput_warp{ background: #FFF; border-radius: 10px; min-width: 320px; }
	.numInput_title{ justify-content: center; height: 54px; width: 100%;min-width: 320px; }
	.numInput_close{ position:absolute;right: 0; height: 54px; width: 54px;}
</style>
<template>
	<view class="page_box" :style="`display:${dataMore.show?'flex':'none'};background:rgba(0,0,0,0.4);`">
		{
			dataMore.show && [
				<view style="flex:1;height:1px;align-items: center; justify-content: center;width:100%;">
					<view class="numInput_warp xy_center">
						<view class="watermark_box">
							{
								G.watermark && 
								<image class="xy_100" :src="G.watermark" mode="aspectFill" thumbnail="false"></image>
							}
						</view>
						<view class="numInput_title flex_row">
							<view @click="closePage()" class="numInput_close xy_center">
								<a-iconfont name="cuohao" color="#666666" :size="G.appFontSize+6"/>
							</view>
							<text :style="`${loadConfiguration(1)}color: #333;font-weight: 600;`">{dataMore.title}</text>
						</view>
						<view style="width:100%;padding:9px 9px 25px;">
							<password-input :dataMore="dataMore"></password-input>
						</view>
					</view>
				</view>,
				<number-keyboard
					:dataMore="dataMore"
					@finish="finish"
					@close="closePage()">
				</number-keyboard>,
				<view :style="`background:#FFF;padding-bottom:${safeArea().bottom}px;`"></view>
			]
		}
	</view>
</template>
<script>
import '../../components/_other/password-input.stml'
import '../../components/_other/number-keyboard.stml'
import { G,loadConfiguration } from '../../script/_zy/general';
import { safeArea } from '../../script/_zy/t';
export default {
	name: 'input-num',
	props:{
		dataMore:Object,
	},
	methods:{
		closePage(){
			this.props.dataMore.show = false;
		},
		finish(){
			G.numInputPop.callback();
		},
	}
};
</script>
